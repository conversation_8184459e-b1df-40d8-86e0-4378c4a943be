"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code') {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\nfunction MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: {\n                        // Headers\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, void 0),\n                        h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, void 0),\n                        // Paragraphs\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, void 0),\n                        // Bold and italic\n                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, void 0),\n                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, void 0),\n                        // Lists\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, void 0),\n                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, void 0),\n                        // Code blocks and inline code\n                        code: ({ node, inline, className, children, ...props })=>{\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, void 0),\n                        // Links\n                        a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, void 0),\n                        // Tables\n                        table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, void 0),\n                        thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, void 0),\n                        tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, void 0),\n                        tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, void 0),\n                        th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, void 0),\n                        td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, void 0),\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;